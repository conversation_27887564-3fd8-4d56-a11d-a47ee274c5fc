# Application name that will be displayed in logs and management interfaces
spring.application.name=File Manager

# Maximum size allowed for individual file uploads (500MB)
# This prevents memory issues and potential DoS attacks from large file uploads
spring.servlet.multipart.max-file-size=500MB

# Maximum size allowed for the entire HTTP request (1GB)
# This includes all files and form data in a single request
# Should be equal to or greater than max-file-size if uploading multiple files
spring.servlet.multipart.max-request-size=1GB

# Multipart file upload configuration
spring.servlet.multipart.enabled=true
spring.servlet.multipart.file-size-threshold=2KB
spring.servlet.multipart.resolve-lazily=false

# Server timeout configuration for large file uploads
server.tomcat.connection-timeout=300000
server.tomcat.max-swallow-size=1GB

# AWS Configuration
spring.cloud.aws.region.static=ap-southeast-1
aws.bucket.name=storage-fm

# Thymeleaf Configuration
spring.thymeleaf.prefix=classpath:/templates/
spring.thymeleaf.suffix=.html
spring.thymeleaf.mode=HTML
spring.thymeleaf.encoding=UTF-8
spring.thymeleaf.cache=false

# Google OAuth2 Configuration
spring.security.oauth2.client.registration.google.client-id=${GOOGLE_CLIENT_ID}
spring.security.oauth2.client.registration.google.client-secret=${GOOGLE_CLIENT_SECRET}
spring.security.oauth2.client.registration.google.scope=openid,profile,email
spring.security.oauth2.client.registration.google.redirect-uri=http://localhost:8080/login/oauth2/code/google

# OAuth2 Provider Configuration
spring.security.oauth2.client.provider.google.authorization-uri=https://accounts.google.com/o/oauth2/auth
spring.security.oauth2.client.provider.google.token-uri=https://oauth2.googleapis.com/token
spring.security.oauth2.client.provider.google.user-info-uri=https://www.googleapis.com/oauth2/v2/userinfo
spring.security.oauth2.client.provider.google.user-name-attribute=id

# Logging Configuration
logging.level.root=INFO
logging.level.org.example=DEBUG
logging.level.software.amazon.awssdk=DEBUG
logging.level.org.springframework.security=DEBUG
logging.pattern.console=%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n


